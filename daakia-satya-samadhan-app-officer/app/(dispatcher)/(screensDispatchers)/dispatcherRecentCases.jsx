import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  StatusBar,
  SafeAreaView,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { useRouter, useFocusEffect } from 'expo-router'; 
import { useAuth } from '../../../context/auth-context';
import { transformUrl } from '../../../utils/transformUrl';
import { apiService } from '../../../services/api';
import { Colors } from '../../../constants/colors';



const DispatcherRecentCases = () => {
  const { token } = useAuth();
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { width } = useWindowDimensions();
  
  // Determine items per row based on screen width
  const getItemsPerRow = (screenWidth) => {
    if (screenWidth >= 1600) return 6;
    if (screenWidth >= 1300) return 5;
    if (screenWidth >= 1000) return 4;
    if (screenWidth >= 700) return 3;
    return 2; 
  };
  const itemsPerRow = getItemsPerRow(width);
  const HORIZONTAL_PADDING = 16;
  const ITEM_MARGIN = 8;
  const THUMBNAIL_SIZE = (width - (HORIZONTAL_PADDING * 2) - (ITEM_MARGIN * 2 * itemsPerRow)) / itemsPerRow;

  // Fetch data whenever the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchRecentCases();
    }, [token])
  );

  const fetchRecentCases = async () => {
    setLoading(true);
    setError(null);

    try {
      // OLD API - Commented out
      // console.log('Making API request with token:', token ? 'Token exists' : 'No token');
      // const response = await apiService.fetchDispatcherRecentCases(token);

      // NEW API - Using Evidence Package API
      // console.log('Fetching evidence packages with new API...');
      const type = "dispatch";
      const status = "dispatched";
      const evidencePackageResponse = await apiService.getEvidencePackageBasedOnTypeAndStatus(
        token,
        type,
        status
      );

      // console.log('Evidence Package API Response:', JSON.stringify(evidencePackageResponse, null, 2));

      if (evidencePackageResponse.status === 'success' && evidencePackageResponse.data) {
        // Transform the new API response to match the expected format
        const transformedCases = evidencePackageResponse.data.map(packageItem => {
          // Extract unique case titles from evidenceId array
          const caseTitles = [];
          const caseIds = [];

          if (packageItem.evidenceId && Array.isArray(packageItem.evidenceId)) {
            packageItem.evidenceId.forEach(evidence => {
              if (evidence.caseId && evidence.caseId.title) {
                // Add case title if not already present (to handle duplicates)
                if (!caseTitles.includes(evidence.caseId.title)) {
                  caseTitles.push(evidence.caseId.title);
                }
                // Add case ID if not already present
                if (!caseIds.includes(evidence.caseId._id)) {
                  caseIds.push(evidence.caseId._id);
                }
              }
            });
          }

          return {
            _id: packageItem._id,
            boxId: packageItem._id, 
            evidenceIds: packageItem.evidenceId, 
            evidenceCount: packageItem.evidenceId ? packageItem.evidenceId.length : 0, 
            packagingUrl: packageItem.packagingUrl, 
            caseTitles: caseTitles,
            caseIds: caseIds, 
            type: packageItem.type,
            status: packageItem.status,
            qrCode: packageItem.qrCode,
            time: packageItem.time,
            createdAt: packageItem.createdAt,
            updatedAt: packageItem.updatedAt,
            createdBy: packageItem.createdBy
          };
        });

        // Sort cases by createdAt date in descending order (newest first)
        const sortedCases = transformedCases.sort((a, b) => {
          const dateA = new Date(a.createdAt);
          const dateB = new Date(b.createdAt);
          return dateB - dateA; // Descending order (newest first)
        });

        setCases(sortedCases);
      } else {
        console.log('Failed Response Structure:', {
          status: evidencePackageResponse.status,
          hasData: !!evidencePackageResponse.data,
          fullResponse: evidencePackageResponse
        });
        setError('Failed to fetch evidence packages');
      }
    } catch (err) {
      console.error('API Error Details:', {
        message: err.message,
        name: err.name,
        response: err.response ? {
          status: err.response.status,
          data: err.response.data,
          headers: err.response.headers
        } : 'No response data',
        request: err.request ? 'Request was made but no response received' : 'No request was made',
        config: err.config ? {
          url: err.config.url,
          method: err.config.method,
          headers: err.config.headers
        } : 'No config data'
      });
      setError('An error occurred while fetching evidence packages');
    } finally {
      setLoading(false);
    }
  };

  // Updated to handle new Evidence Package API structure
  const handlePackagePress = (packageItem) => {
    // Navigate to next page with the package data
    // Using boxId (_id) and evidenceIds array as mentioned in requirements
    const packageData = {
      boxId: packageItem._id, // Use _id as boxId
      evidenceIds: packageItem.evidenceIds, // Array of evidence IDs
      evidenceCount: packageItem.evidenceCount, // Count from array length
      packagingUrl: packageItem.packagingUrl, // Array of packaging URLs
      caseTitles: packageItem.caseTitles, // Array of case titles
      caseIds: packageItem.caseIds, // Array of case IDs
      type: packageItem.type,
      status: packageItem.status,
      qrCode: packageItem.qrCode,
      time: packageItem.time
    };

    router.push({
      pathname: '(screensDispatchers)/ForensicRequestDetails', // Update this to your actual next page route
      params: { packageData: JSON.stringify(packageData) },
    });
  };

  const renderPackageItem = (item) => {
    // Extract first packaging URL from the array
    const packageImageUrl = item.packagingUrl && item.packagingUrl.length > 0
      ? item.packagingUrl[0].split(',')[0] // Handle comma-separated URLs by taking the first one
      : null;

    return (
      <TouchableOpacity
        key={item._id}
        style={[styles.packageItem, {
          width: THUMBNAIL_SIZE,
          height: THUMBNAIL_SIZE + 50,
          margin: ITEM_MARGIN
        }]}
        onPress={() => handlePackagePress(item)}
        testID={`package-item-${item._id}`}
      >
        {packageImageUrl ? (
          <Image
            source={{ uri: transformUrl(packageImageUrl) }}
            style={styles.packageImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.packageImage, styles.noImageContainer]}>
            <Text style={styles.noImageText}>No Image</Text>
          </View>
        )}
        <View style={styles.packageLabelContainer}>
          {item.caseTitles && item.caseTitles.length > 0 ? (
            <>
              <Text style={styles.caseNameLabel}>Case Name</Text>
              <Text style={styles.caseNameText} numberOfLines={2} ellipsizeMode="tail">
                {item.caseTitles.join(', ')}
              </Text>
            </>
          ) : (
            <Text style={styles.packageLabel} numberOfLines={2} ellipsizeMode="tail">
              Evidence Package #{item._id.slice(-6)}
            </Text>
          )}
          <Text style={styles.packageMeta}>
            {item.evidenceCount} Evidence{item.evidenceCount !== 1 ? 's' : ''}
            {item.caseTitles && item.caseTitles.length > 1 && (
              <Text style={styles.multipleCasesText}> • {item.caseTitles.length} Cases</Text>
            )}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#0a34a1" />
          <Text style={styles.loadingText}>Loading evidence packages...</Text>
        </View>
      );
    }
    
    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchRecentCases}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    if (cases.length === 0) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.emptyText}>No evidence packages available</Text>
        </View>
      );
    }
    
    return (
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        <View style={styles.headerContainer}>
          <Text style={styles.sectionTitle}>
            Evidence Packages Box ({cases.length})
          </Text>
          <TouchableOpacity 
            style={styles.refreshButton} 
            onPress={fetchRecentCases}
            testID="refresh-button"
          >
            <MaterialCommunityIcons name="refresh" size={16} color="#0a34a1" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.gridContainer}>
          {cases.map(item => renderPackageItem(item))}
        </View>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <StatusBar style="dark" />
      <View style={styles.container}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};



// Styles remain unchanged
const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0a34a1',
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Roboto_bold',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#0a34a1',
    backgroundColor: '#F8F9FA',
    height: 28,
  },
  refreshButtonText: {
    color: '#0a34a1',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
    fontFamily: 'Roboto',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  packageItem: {
    borderRadius: 10,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  packageImage: {
    width: '100%',
    height: '70%',
  },
  noImageContainer: {
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    color: '#757575',
    fontSize: 14,
  },
  packageLabelContainer: {
    padding: 10,
    backgroundColor: '#fff',
    height: '30%',
    justifyContent: 'space-between',
  },
  packageLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    fontFamily: 'Roboto',
  },
  caseNameLabel: {
    fontSize: 11,
    fontWeight: '400',
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  caseNameText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    fontFamily: 'Roboto',
  },
  packageMeta: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
    fontFamily: 'Roboto',
  },
  multipleCasesText: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontFamily: 'Roboto',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Roboto',
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#0a34a1',
    borderRadius: 4,
    fontFamily: 'Roboto_bold',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
  },
});

export default DispatcherRecentCases;