import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, Modal, Alert, ActivityIndicator } from "react-native";
import QRScanner from "../../../components/Larges/QRScanner";
import { useRouter } from 'expo-router';
import Constants from 'expo-constants';
import { useAuth } from '../../../context/auth-context';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

export default function HomeDispatcher() {
  const router = useRouter();
  const { token } = useAuth();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const segregateEvidences = (caseItem) => {
    const forensicRequestId = caseItem._id;
    const segregatedEvidences = {};
    
    caseItem.evidences.forEach(evidence => {
      const labId = evidence.labId._id;
      const labDepartmentId = evidence.labDepartmentId._id;
      
      if (!segregatedEvidences[labId]) {
        segregatedEvidences[labId] = {
          name: evidence.labId.name,
          departments: {}
        };
      }
      
      if (!segregatedEvidences[labId].departments[labDepartmentId]) {
        segregatedEvidences[labId].departments[labDepartmentId] = {
          name: evidence.labDepartmentId.name,
          evidences: []
        };
      }
      
      segregatedEvidences[labId].departments[labDepartmentId].evidences.push({
        ...evidence,
        status: caseItem.status,
        forensicRequestId
      });
    });
    
    return segregatedEvidences;
  };

  const handleScanComplete = async (data) => {
    try {
      // Parse the URL
      const url = new URL(data);
      const path = url.pathname;
      

      if (path.includes('/cases/')) {

        setIsModalVisible(false);
        Alert.alert(
          "Officer QR Code",
          `Please use the Officer scanner for ${path.includes('/evidences/') ? 'case evidence' : 'case'} details`,
          [{ text: "OK" }],
          { cancelable: false }
        );
      }
      else if (path.includes('/evidencePackaging/')) {
        // Extract box ID from evidence packaging URL
        const boxId = path.split('/')[2];
        
        setIsModalVisible(false);
        router.push({
          pathname: '(dispatcher)/(screensDispatchers)/ForensicRequestDetails',
          params: { 
            packageData: JSON.stringify({
              boxId: boxId,
              caseIds: [] // Will be populated by the screen
            })
          },
        });
      }
      else if (path.includes('/forensicRequests/')) {
        // Extract forensic request ID
        const forensicRequestId = path.split('/')[2];
        
        // Show loading state
        setLoading(true);
        
        try {
          // Fetch forensic request data to verify the ID exists
          const response = await fetch(`${BASE_URL}/api/case/forensic/dispatch`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          const responseData = await response.json();
          
          if (responseData.status === 'success' && responseData.data?.cases) {
            // Find the matching forensic request
            const matchedCase = responseData.data.cases.find(item => item._id === forensicRequestId);
            
            if (matchedCase) {
              // Found matching case, prepare data for navigation
              const caseData = {
                forensicRequestId: matchedCase._id,
                caseId: matchedCase.caseId._id,
                caseTitle: matchedCase.caseId.title,
                caseDescription: matchedCase.caseId.description,
                status: matchedCase.status,
                segregatedEvidences: segregateEvidences(matchedCase),
                fromQr: true // Flag to indicate this came from QR scan
              };
              
             
              setIsModalVisible(false);
              router.push({
                pathname: '(dispatcher)/(screensDispatchers)/ForensicRequestDetails',
                params: { caseData: JSON.stringify(caseData) },
              });
            } else {
              // Forensic request ID not found
              setIsModalVisible(false);
              Alert.alert(
                "Forensic Request Not Found",
                "The scanned QR code contains a forensic request ID that could not be found",
                [{ text: "OK" }],
                { cancelable: false }
              );
            }
          } else {
            // API error
            setIsModalVisible(false);
            Alert.alert(
              "Error",
              "Failed to verify forensic request",
              [{ text: "OK" }],
              { cancelable: false }
            );
          }
        } catch (error) {
          console.error("API Error:", error);
          setIsModalVisible(false);
          Alert.alert(
            "Error",
            "An error occurred while fetching forensic request data",
            [{ text: "OK" }],
            { cancelable: false }
          );
        } finally {
          setLoading(false);
        }
      }
      else {
        // Unknown URL format
        setIsModalVisible(false);
        Alert.alert(
          "Invalid QR Code",
          "The scanned QR code is not in a recognized format",
          [{ text: "OK" }],
          { cancelable: false }
        );
      }
    } catch (error) {
      // Handle parsing errors
      console.error("Error parsing URL:", error);
      setIsModalVisible(false);
      Alert.alert(
        "Invalid QR Code",
        "Could not process the scanned QR code",
        [{ text: "OK" }],
        { cancelable: false }
      );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.caseButton} 
          onPress={() => router.push('(dispatcher)/(screensDispatchers)/dispatcherRecentCases')}
        >
          <Image 
            source={require('../../../assets/images/recent_cases.png')} 
            style={styles.buttonIcon} 
          />
          <Text style={styles.buttonText}>View packages</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.qrSection} onPress={() => setIsModalVisible(true)}>
        <Text style={styles.qrTitle}>View a case</Text>
        <Text style={styles.qrSubtitle}>Scan QR to view any case and evidences</Text>
        <Image 
          source={require('../../../assets/images/image.png')} 
          style={styles.qrImage} 
        />
      </TouchableOpacity>

      <Modal visible={isModalVisible} animationType="slide">
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0a34a1" />
            <Text style={styles.loadingText}>Verifying QR code...</Text>
          </View>
        ) : (
          <QRScanner 
            onScanComplete={handleScanComplete} 
            onClose={() => setIsModalVisible(false)} 
          />
        )}
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    padding: 30,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 50,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",
  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0B36A1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: "#0B36A1",
    textAlign: "center",
    fontFamily: "Roboto_bold",
  },
  qrSection: {
    alignItems: "center",
    marginVertical: 30,
  },
  qrTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#0B36A1",
    fontFamily: "Roboto_bold",
  },
  qrSubtitle: {
    fontSize: 16,
    color: "gray",
    marginBottom: 15,
    fontFamily: "Roboto",
  },
  qrImage: {
    width: 180,
    height: 180,
    resizeMode: "contain",
    marginLeft: 13,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    fontFamily: "Roboto",
  },
});