import React, { useState, useCallback, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Modal,
  Alert,
  Linking,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SelectList } from 'react-native-dropdown-select-list';
import { useAuth } from '../../../context/auth-context';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import LocationSelector from '../../../components/Larges/LocationSelector';
import * as Location from 'expo-location';
import * as IntentLauncher from 'expo-intent-launcher';
import { Colors } from '../../../constants/colors';
import { apiService } from '../../../services/api';
import LocationPermissionWarning from '../../../components/Smalls/LocationPermissionWarning';
import { getCurrentTime } from '../../../utils/currentTime';

const CRIME_TYPES = [
  { label: 'Cyber Crime', value: 'Cyber Crime' },
  { label: 'Environmental Crime', value: 'Environmental Crime' },
  { label: 'Hate Crime', value: 'Hate Crime' },
  { label: 'Organized Crime', value: 'Organized Crime' },
  { label: 'Property Crime', value: 'Property Crime' },
  { label: 'Public Order Crime', value: 'Public Order Crime' },
  { label: 'Sex Crime', value: 'Sex Crime' },
  { label: 'Traffic Crime', value: 'Traffic Crime' },
  { label: 'Violent Crime', value: 'Violent Crime' },
  { label: 'White-Collar Crime', value: 'White-Collar Crime' },
];

// Styles
const STYLES = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#FFFFFF' },
  scrollContainer: { padding: 20 },
  headerText: { fontSize: 16, marginBottom: 20, color: '#858080' },
  errorContainer: { backgroundColor: '#FEE2E2', padding: 10, borderRadius: 8, marginBottom: 20 },
  errorText: { color: '#DC2626' },
  form: { gap: 15 },
  label: { fontSize: 16, fontWeight: '500', color:'#858080' },
  input: { borderWidth: 1, borderColor: '#BFC7D2', borderRadius: 8, padding: 10, fontSize: 16 },
  disabledInput: { backgroundColor: '#F5F5F5', color: '#666' },
  submitButton: { backgroundColor: '#0B36A1', padding: 15, borderRadius: 20, alignItems: 'center', marginTop: 20 },
  submitButtonText: { color: '#FFFFFF', fontSize: 16, fontWeight: 'bold' },
  modalOverlay: { flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.5)', justifyContent: 'center', alignItems: 'center' },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalTitle: { fontSize: 22, fontWeight: 'bold', marginBottom: 16, textAlign: 'center' },
  modalDescription: { fontSize: 16, color: '#858080', marginBottom: 24, textAlign: 'center' },
  modalButtonContainer: { flexDirection: 'row', justifyContent: 'space-between' },
  modalButton: { flex: 1, padding: 15, borderRadius: 20, alignItems: 'center', marginHorizontal: 8 },
  cancelButton: { backgroundColor: '#FFFFFF', borderWidth: 1, borderColor: '#CCCCCC' },
  createButton: { backgroundColor: '#0B36A1' },
  cancelButtonText: { color: '#666666', fontWeight: 'bold', fontSize: 16 },
  createButtonText: { color: '#FFFFFF', fontWeight: 'bold', fontSize: 16 },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  addressField: { flexDirection: 'row', alignItems: 'center' },
  mapButton: { marginLeft: 10, padding: 10, justifyContent: 'center', alignItems: 'center' },
  locationSelectorModal: { flex: 1, backgroundColor: '#fff' },
  locationWarningContainer: {
    backgroundColor: '#FEE2E2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#DC2626',
  },
  locationWarningTitle: {
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    color: Colors.black,
    marginBottom: 8,
  },
  locationWarningText: {
    fontFamily: 'Roboto',
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 16,
    lineHeight: 20,
  },
  locationCheckButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  locationCheckButtonText: {
    fontFamily: 'Roboto_bold',
    color: Colors.background,
    fontSize: 16,
    marginLeft: 8,
  },
});

const NewCase = React.memo(() => {
  const navigation = useNavigation();
  const { profile, token } = useAuth();
  const [errorMsg, setErrorMsg] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showLocationSelector, setShowLocationSelector] = useState(false);
  const [locationEnabled, setLocationEnabled] = useState(false);
  const [isCheckingLocation, setIsCheckingLocation] = useState(true);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    firNo: '',
    address1: '',
    address2: '',
    state: '',
    city: '',
    pincode: '',
    crimeType: '',
    remarks: '',
    latitude: null,
    longitude: null,
    date: getCurrentTime.indian().split(' ')[0], // DD/MM/YYYY format
    time: getCurrentTime.time12(), // 12-hour format with AM/PM
  });

  useEffect(() => {
    checkLocationPermissions();
  }, []);

  const checkLocationPermissions = async () => {
    try {
      setIsCheckingLocation(true);
      const serviceEnabled = await Location.hasServicesEnabledAsync();
      if (!serviceEnabled) {
        setLocationEnabled(false);
        return;
      }

      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationEnabled(status === 'granted');
    } catch (error) {
      console.error('Error checking location permissions:', error);
      setLocationEnabled(false);
    } finally {
      setIsCheckingLocation(false);
    }
  };

  const handlePermissionGranted = useCallback(() => {
    setLocationEnabled(true);
  }, []);

  const handleInputChange = useCallback((field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setErrorMsg(null); // Clear error on input change
  }, []);

  const handleLocationSelected = useCallback((data) => {
    setFormData((prev) => ({
      ...prev,
      address1: data.address1,
      city: data.city,
      state: data.state,
      pincode: data.pincode,
      latitude: data.latitude,
      longitude: data.longitude,
    }));
    setShowLocationSelector(false);
  }, []);

  const validateAndConfirm = useCallback(() => {
    if (!locationEnabled) {
      Alert.alert(
        'Location Required',
        'Please enable location services and grant permission to continue.',
        [
          { text: 'Check Permissions', onPress: checkLocationPermissions },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
      return;
    }

    const { title, description, firNo, address1, address2, state, city, pincode, crimeType, remarks, latitude, longitude } = formData;
    if (!title) return Alert.alert('Error', 'Title is required');
    if (!description) return Alert.alert('Error', 'Description is required');
    if (!address1 || !latitude || !longitude) return Alert.alert('Error', 'Please select a location using the map');
    if (!address2) return Alert.alert('Error', 'Address 2 is required');
    if (!state) return Alert.alert('Error', 'State is required');
    if (!city) return Alert.alert('Error', 'City is required');
    if (!pincode || !/^\d{5,6}$/.test(pincode)) return Alert.alert('Error', 'Valid Pincode is required (5-6 digits)');
    if (!crimeType) return Alert.alert('Error', 'Crime Type is required');
    if (!remarks) return Alert.alert('Error', 'Remarks are required');
    
    setShowConfirmation(true);
  }, [formData, locationEnabled]);

  const handleCreateCase = useCallback(async () => {
    setIsLoading(true);
    setShowConfirmation(false);
  
    try {
      const payload = {
        title: formData.title,
        description: formData.description,
        firNumber: formData.firNo ? [formData.firNo] : [], 
        caseType: formData.crimeType,
        address1: formData.address1,
        address2: formData.address2,
        state: formData.state,
        city: formData.city,
        pincode: formData.pincode,
        gpsLocation: `latitude:${formData.latitude},longitude:${formData.longitude}`,
        remarks: formData.remarks,
        department: profile.policeProfile.department._id,
        policeStation: profile.policeProfile.policeStation._id,
        createdAt: getCurrentTime.now(), // ISO timestamp for server
        localDate: formData.date, // Local date for display
        localTime: formData.time, // Local time for display
      };
  
      // Log the payload before sending the request
      // console.log('Payload being sent:', JSON.stringify(payload, null, 2));
  
      const result = await apiService.createCase(token, payload);
  
      // Log the response data
      // console.log('Response data:', JSON.stringify(result, null, 2));
  
      if (result.data) {
        router.replace({
          pathname: '(screens)/premisesCapture',
          params: { caseId: result.data._id },
        });
      } else {
        Alert.alert('Error', result.message || 'Failed to create case');
      }
    } catch (error) {
      console.error('Error creating case:', error);
      Alert.alert('Error', error.message || 'Failed to connect to server');
    } finally {
      setIsLoading(false);
    }
  }, [formData, profile, token]);

  const ConfirmationModal = () => (
    <Modal
      visible={showConfirmation}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowConfirmation(false)}
    >
      <View style={STYLES.modalOverlay}>
        <View style={STYLES.modalContainer}>
          <Text style={STYLES.modalTitle}>Are you sure you want to add this case?</Text>
          <Text style={STYLES.modalDescription}>
            Once a case is created, it cannot be deleted. Please ensure all information
            entered is accurate before proceeding.
          </Text>
          <View style={STYLES.modalButtonContainer}>
            <TouchableOpacity
              style={[STYLES.modalButton, STYLES.cancelButton]}
              onPress={() => setShowConfirmation(false)}
            >
              <Text style={STYLES.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[STYLES.modalButton, STYLES.createButton]}
              onPress={handleCreateCase}
            >
              <Text style={STYLES.createButtonText}>Create</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={STYLES.container}>
      <ScrollView contentContainerStyle={STYLES.scrollContainer}>
        <Text style={STYLES.headerText}>
          Fill in the details of the new case, including case name, description, location,
          involved parties, and initial evidence. Ensure accuracy to assist in a thorough
          investigation.
        </Text>

        {!isCheckingLocation && !locationEnabled && (
          <LocationPermissionWarning onPermissionGranted={handlePermissionGranted} />
        )}

        {errorMsg && (
          <View style={STYLES.errorContainer}>
            <Text style={STYLES.errorText}>{errorMsg}</Text>
          </View>
        )}

        <View style={STYLES.form}>
          <Text style={STYLES.label}>Date</Text>
          <TextInput style={[STYLES.input, STYLES.disabledInput]} value={formData.date} editable={false} />

          <Text style={STYLES.label}>Time</Text>
          <TextInput style={[STYLES.input, STYLES.disabledInput]} value={formData.time} editable={false} />

          <Text style={STYLES.label}>Title *</Text>
          <TextInput
            style={STYLES.input}
            placeholder="Enter title"
            value={formData.title}
            onChangeText={(value) => handleInputChange('title', value)}
          />

          <Text style={STYLES.label}>Description *</Text>
          <TextInput
            style={[STYLES.input, { height: 100, textAlignVertical: 'top' }]}
            placeholder="Enter description"
            multiline
            value={formData.description}
            onChangeText={(value) => handleInputChange('description', value)}
          />

          <Text style={STYLES.label}>FIR No</Text>
          <TextInput
            style={STYLES.input}
            placeholder="Enter FIR No"
            value={formData.firNo}
            onChangeText={(value) => handleInputChange('firNo', value)}
          />

          <Text style={STYLES.label}>Address 1 *</Text>
          <View style={STYLES.addressField}>
            <TextInput
              style={[STYLES.input, STYLES.disabledInput, { flex: 1 }]}
              placeholder="Select from map"
              value={formData.address1}
              editable={false}
            />
            <TouchableOpacity style={STYLES.mapButton} onPress={() => setShowLocationSelector(true)}>
              <Ionicons name="map-outline" size={24} color="#0B36A1" />
            </TouchableOpacity>
          </View>

          <Text style={STYLES.label}>Address 2 *</Text>
          <TextInput
            style={STYLES.input}
            placeholder="Enter address 2"
            value={formData.address2}
            onChangeText={(value) => handleInputChange('address2', value)}
          />

          <Text style={STYLES.label}>Latitude & Longitude</Text>
          <TextInput
            style={[STYLES.input, STYLES.disabledInput]}
            value={formData.latitude && formData.longitude ? `${formData.latitude}, ${formData.longitude}` : 'Not selected'}
            editable={false}
          />

          <Text style={STYLES.label}>City *</Text>
          <TextInput
            style={[STYLES.input, STYLES.disabledInput]}
            placeholder="Select from map"
            value={formData.city}
            editable={false}
          />

          <Text style={STYLES.label}>State *</Text>
          <TextInput
            style={[STYLES.input, STYLES.disabledInput]}
            placeholder="Select from map"
            value={formData.state}
            editable={false}
          />

          <Text style={STYLES.label}>Pincode *</Text>
          <TextInput
            style={STYLES.input}
            placeholder="Select from map or enter manually"
            value={formData.pincode}
            onChangeText={(value) => {
              // Only allow numeric input with max 6 digits
              const numericValue = value.replace(/[^0-9]/g, '').slice(0, 6);
              handleInputChange('pincode', numericValue);
            }}
            keyboardType="numeric"
            maxLength={6}
          />

          <Text style={STYLES.label}>Crime Type *</Text>
          <SelectList
            setSelected={(value) => handleInputChange('crimeType', value)}
            data={CRIME_TYPES}
            placeholder="Select crime type"
            save="value"
          />

          <Text style={STYLES.label}>Department Code</Text>
          <TextInput
            style={[STYLES.input, STYLES.disabledInput]}
            value={profile?.policeProfile?.department?.code || 'N/A'}
            editable={false}
          />

          <Text style={STYLES.label}>Police Station Name</Text>
          <TextInput
            style={[STYLES.input, STYLES.disabledInput]}
            value={profile?.policeProfile?.policeStation?.name || 'N/A'}
            editable={false}
          />

          <Text style={STYLES.label}>Remarks by IO *</Text>
          <TextInput
            style={[STYLES.input, { height: 100, textAlignVertical: 'top' }]}
            placeholder="Enter remarks"
            multiline
            value={formData.remarks}
            onChangeText={(value) => handleInputChange('remarks', value)}
          />

          <TouchableOpacity style={STYLES.submitButton} onPress={validateAndConfirm}>
            <Text style={STYLES.submitButtonText}>Submit</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <ConfirmationModal />
      {isLoading && (
        <View style={STYLES.loadingOverlay}>
          <Text>Creating case...</Text>
        </View>
      )}
      <Modal
        visible={showLocationSelector}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLocationSelector(false)}
      >
        <View style={STYLES.locationSelectorModal}>
          <LocationSelector
            onClose={() => setShowLocationSelector(false)}
            onConfirm={handleLocationSelected}
          />
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
});

export default NewCase;
