import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  BackHandler,
  Text,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import FormSection from '../../../components/Smalls/FormSection';
import PhotoCapture from '../../../components/Larges/PhotoCapture';
import VideoRecordingComp from '../../../components/Larges/VideoRecordingComp';
import useUploadMedia from '../../../hooks/useUploadMedia';
import Constants from 'expo-constants';
import { useLocalSearch<PERSON>ara<PERSON>, useRouter } from 'expo-router';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import { apiService } from '../../../services/api';

const PremisesCapture = () => {
  const { caseId } = useLocalSearchParams();
  // console.log('caseId', caseId);
  const router = useRouter();
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [videoVisible, setVideoVisible] = useState(false);
  const [description, setDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const { uploadMedia, error } = useUploadMedia();
  const { token } = useAuth();

  useEffect(() => {
    const backAction = () => {
      if (cameraVisible) {
        setCameraVisible(false);
        return true;
      }
      if (videoVisible) {
        setVideoVisible(false);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove();
  }, [cameraVisible, videoVisible]);

  const handleCaptureOption = (option) => {
    setShowOptions(false);
    if (option === 'photo') {
      setCameraVisible(true);
    } else if (option === 'video') {
      setVideoVisible(true);
    }
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  const handlePhotoCaptured = (uriOrUris) => {
    if (Array.isArray(uriOrUris)) {
      // Batch mode - multiple photos
      const newMedia = uriOrUris.map(uri => ({
        id: `${Date.now()}_${Math.random()}`,
        uri,
        type: 'image',
        uploaded: false
      }));
      setMedia((prevMedia) => [...prevMedia, ...newMedia]);
    } else {
      // Single mode - one photo
      const mediaId = Date.now().toString();
      setMedia((prevMedia) => [
        ...prevMedia,
        { id: mediaId, uri: uriOrUris, type: 'image', uploaded: false }
      ]);
    }
    setCameraVisible(false);
  };

  const handleVideoCaptured = (uri) => {
    const mediaId = Date.now().toString();
    setMedia((prevMedia) => [
      ...prevMedia,
      { id: mediaId, uri, type: 'video', uploaded: false }
    ]);
    setVideoVisible(false);
  };

  const handleBackPressed = () => {
    setVideoVisible(false);
  };

  const handleRecordingComplete = () => {
    setVideoVisible(false);
  };

  const handleSubmit = async () => {
    // Validation checks
    if (!caseId) {
      Alert.alert('Error', 'No case ID provided');
      return;
    }

    if (media.length === 0) {
      Alert.alert('Error', 'No media selected');
      return;
    }

    if (!description || description.trim() === '') {
      Alert.alert('Error', 'Please provide a description');
      return;
    }

    setIsUploading(true);
    setIsSaving(true);

    try {
  
      const uploadPromises = media.map((item) => {
        return uploadMedia(item.uri, item.type)
          .then(url => {
            // Update the media item status
            setMedia(prevMedia => 
              prevMedia.map(mediaItem => 
                mediaItem.id === item.id 
                  ? { ...mediaItem, uploaded: true, url } 
                  : mediaItem
              )
            );
            setUploadProgress(prev => prev + (1 / media.length * 100));
            return url;
          })
          .catch(err => {
            console.error(`Failed to upload ${item.type}:`, err);
            return null; // Return null for failed uploads
          });
      });

      const uploadResults = await Promise.all(uploadPromises);
      
    
      const successfulUploads = uploadResults.filter(Boolean);
      const failedUploads = uploadResults.length - successfulUploads.length;
      
      if (failedUploads > 0) {

        Alert.alert(
          'Upload Warning',
          `${failedUploads} out of ${uploadResults.length} uploads failed. Do you want to continue with the successfully uploaded media?`,
          [
            { 
              text: 'Cancel', 
              style: 'cancel', 
              onPress: () => {
                setIsSaving(false);
                setIsUploading(false);
                setUploadProgress(0);
              } 
            },
            { 
              text: 'Continue', 
              onPress: () => completeSubmission(successfulUploads) 
            }
          ]
        );
      } else if (successfulUploads.length > 0) {
        // All uploads succeeded
        await completeSubmission(successfulUploads);
      } else {
        // All uploads failed
        Alert.alert('Error', 'All media uploads failed. Please try again.');
        setIsSaving(false);
        setIsUploading(false);
        setUploadProgress(0);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed during upload process: ' + (error.message || 'Unknown error'));
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Helper function to complete the submission
  const completeSubmission = async (uploadedUrls) => {
    try {
      const payload = {
        premisesImageUrl: uploadedUrls,
        premiseDescription: description.trim(),
      };

      const result = await apiService.updatePremises(token, caseId, payload);

      if (result.data) {
        setIsSuccessVisible(true);
      } else {
        Alert.alert('Error', result.message || 'Failed to save premises data');
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'An error occurred while saving');
    } finally {
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 20 }}>
          <CapturePageHeader
            title="Capture Premises Photos"
            subtitle="Collect and upload all relevant photos of the crime premises for this case. Ensure each item is accurately documented and securely stored to maintain the integrity of the investigation."
          />

          <CaptureButton onPress={() => setShowOptions(!showOptions)} />

          <OptionsOverlay 
            visible={showOptions}
            onSelectOption={handleCaptureOption}
            onClose={() => setShowOptions(false)}
          />

          {media.length > 0 && (
            <>
              <MediaGrid
                media={media}
                onDeleteMedia={handleDeleteMedia}
                thumbnailSize={150}
              />
              {error && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>Error: {error}</Text>
                </View>
              )}

              {isUploading && (
                <View style={styles.uploadSummary}>
                  <Text style={styles.uploadTitle}>
                    Uploading Media... ({Math.round(uploadProgress)}%)
                  </Text>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBar, 
                        { width: `${uploadProgress}%` }
                      ]} 
                    />
                  </View>
                  <View style={styles.uploadingIndicator}>
                    <ActivityIndicator size="small" color="#0066cc" />
                    <Text style={styles.uploadingText}>
                      Uploading {media.length} files...
                    </Text>
                  </View>
                </View>
              )}
            </>
          )}
          
          <FormSection
            description={description}
            onDescriptionChange={setDescription}
            onSubmit={handleSubmit}
            isSaving={isSaving}
            buttonText={isSaving ? "Processing..." : "Save to Case"}
            paddingHorizontal={20}
          />
        </ScrollView>

        {cameraVisible && (
          <View style={styles.cameraOverlay}>
            <PhotoCapture
              setUri={handlePhotoCaptured}
              backPressed={() => setCameraVisible(false)}
            />
          </View>
        )}

        {videoVisible && (
          <View style={styles.cameraOverlay}>
            <VideoRecordingComp
              setUri={handleVideoCaptured}
              backPressed={handleBackPressed}
              onRecordingComplete={handleRecordingComplete}
            />
          </View>
        )}

        {isSuccessVisible && (
          <SuccessScreen
            message="Premises data sync successfully!"
            duration={2000}
            onComplete={() => router.replace({
              pathname: '/(officer)/(screens)/caseDetails',
              params: { caseid: caseId }
            })} 
          />
        )}
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
  },
  errorContainer: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#ffecec',
    borderRadius: 5,
    marginTop: 10,
  },
  errorText: {
    color: '#ff0000',
  },
  uploadSummary: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 5,
    marginTop: 10,
  },
  uploadTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  uploadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  uploadingText: {
    marginLeft: 10,
    color: '#0066cc',
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginVertical: 5,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
});

export default PremisesCapture;