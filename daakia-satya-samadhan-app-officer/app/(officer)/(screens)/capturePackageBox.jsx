import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  BackHandler,
  Text,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import PhotoCapture from '../../../components/Larges/PhotoCapture';
import useUploadMedia from '../../../hooks/useUploadMedia';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import VideoRecordingComp from '../../../components/Larges/VideoRecordingComp';
import { apiService } from '../../../services/api';


const CapturePackageBox = () => {
  const { caseid, evidenceIds, packagingUrls } = useLocalSearchParams();
  const router = useRouter();
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const { uploadMedia, error } = useUploadMedia();
  const { token } = useAuth();
  const [videoVisible, setVideoVisible] = useState(false);

  useEffect(() => {
    const backAction = () => {
      if (cameraVisible) {
        setCameraVisible(false);
        return true;
      }
      if (videoVisible) {
        setVideoVisible(false);
        return true;
      }
      return false;
    };
  
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );
  
    return () => backHandler.remove();
  }, [cameraVisible, videoVisible]);

  const handleCaptureOption = (option) => {
    setShowOptions(false); 
    if (option === 'photo') {
      setCameraVisible(true);
    }
    else if (option === 'video') {
      setVideoVisible(true);
    }
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  const handlePhotoCaptured = (uriOrUris) => {
    if (Array.isArray(uriOrUris)) {
      // Batch mode - multiple photos
      const newMedia = uriOrUris.map(uri => ({
        id: `${Date.now()}_${Math.random()}`,
        uri,
        type: 'image',
        uploaded: false
      }));
      setMedia((prevMedia) => [...prevMedia, ...newMedia]);
    } else {
      // Single mode - one photo
      const mediaId = Date.now().toString();
      setMedia((prevMedia) => [
        ...prevMedia,
        { id: mediaId, uri: uriOrUris, type: 'image', uploaded: false }
      ]);
    }
    setCameraVisible(false);
  };

  const handleVideoCaptured = (uri) => {
    const mediaId = Date.now().toString();
    setMedia((prevMedia) => [
      ...prevMedia,
      { id: mediaId, uri, type: 'video', uploaded: false }
    ]);
    setVideoVisible(false);
  };

  const saveToCase = async () => {
    if (!caseid) {
      Alert.alert('Error', 'No case ID provided');
      return;
    }

    if (!evidenceIds) {
      Alert.alert('Error', 'No evidence IDs provided');
      return;
    }

    if (media.length === 0) {
      Alert.alert('Error', 'Please capture at least one photo of the forensic package box');
      return;
    }

    setIsUploading(true);
    setIsSaving(true);
    setUploadProgress(0);

    try {
      // Step 1: Upload all media files in parallel
      const uploadPromises = media.map((item, index) => {
        return uploadMedia(item.uri, item.type)
          .then(url => {
            // Update the media item status
            setMedia(prevMedia => 
              prevMedia.map(mediaItem => 
                mediaItem.id === item.id 
                  ? { ...mediaItem, uploaded: true, url } 
                  : mediaItem
              )
            );
            // Update progress
            setUploadProgress(prev => prev + (1 / media.length * 100));
            return url;
          })
          .catch(err => {
            console.error(`Failed to upload ${item.type}:`, err);
            return null; // Return null for failed uploads
          });
      });

      const uploadResults = await Promise.all(uploadPromises);
      
      // Step 2: Check for upload failures
      const successfulUploads = uploadResults.filter(Boolean);
      const failedUploads = uploadResults.length - successfulUploads.length;
      
      if (failedUploads > 0) {
        // Some uploads failed - ask user if they want to continue
        Alert.alert(
          'Upload Warning',
          `${failedUploads} out of ${uploadResults.length} uploads failed. Do you want to continue with the successfully uploaded media?`,
          [
            { 
              text: 'Cancel', 
              style: 'cancel', 
              onPress: () => {
                setIsSaving(false);
                setIsUploading(false);
                setUploadProgress(0);
              } 
            },
            { 
              text: 'Continue', 
              onPress: () => completeSubmission(successfulUploads) 
            }
          ]
        );
      } else if (successfulUploads.length > 0) {
        // All uploads succeeded
        await completeSubmission(successfulUploads);
      } else {
        // All uploads failed
        Alert.alert('Error', 'All media uploads failed. Please try again.');
        setIsSaving(false);
        setIsUploading(false);
        setUploadProgress(0);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed during upload process: ' + (error.message || 'Unknown error'));
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };


  const completeSubmission = async (uploadedUrls) => {
    try {
      const evidenceIdsArray = typeof evidenceIds === 'string' ? evidenceIds.split(',') : evidenceIds;

      // Call the API once with all evidence IDs and packaging URLs
      // console.log('📦 Packaging URLs to send (array):', uploadedUrls);
      // console.log('📦 Calling addEvidenceBoxPackaging with:', {
      //   packagingUrl: uploadedUrls, // Array of packaging URLs
      //   evidenceId: evidenceIdsArray, // Array of evidence IDs
      //   type: 'dispatch'
      // });

      const packagingResult = await apiService.addEvidenceBoxPackaging(
        token,
        uploadedUrls, // Send packaging URLs as array
        evidenceIdsArray, // Send all evidence IDs as array
        'dispatch'
      );

      // console.log('📦 Packaging API result:', JSON.stringify(packagingResult, null, 2));

      // Check if packaging API call was successful
      if (!packagingResult || packagingResult.status !== 'success') {
        Alert.alert('Error', 'Failed to add evidence box packaging. Cannot proceed to forensic submission.');
        return;
      }
      const packageBoxQrCode = packagingResult?.data?.qrCode || null;
      
      const result = await apiService.submitCaseToForensic(token, caseid, evidenceIdsArray);
      
      if (result.data) {
        setIsSuccessVisible(true); 
    
        setTimeout(() => {
          router.replace({
            pathname: '(screens)/forensicQr',
            params: { 
              caseid: caseid,
              ForensicQr: packageBoxQrCode, // Using package box QR code
            },
          });
        }, 3000);
      } else {
        Alert.alert('Error', result.message || 'Failed to submit to forensic');
      }
    } catch (error) {
      console.error('Error in completeSubmission:', error);
      Alert.alert('Error', error.message || 'An error occurred while processing the submission');
    } finally {
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 20 }}>
          <CapturePageHeader
            title="Capture Package Box Photo *"
            subtitle="Capture the photo of the box or package in which the selected evidences will be sent to forensic analysis."
          />

          <CaptureButton onPress={() => setShowOptions(!showOptions)} />

          {showOptions && (
            <OptionsOverlay
              visible={showOptions} 
              onSelectOption={handleCaptureOption} 
              onClose={() => setShowOptions(false)} 
            />
          )}

          {media.length > 0 && (
            <>
              <MediaGrid
                media={media}
                onDeleteMedia={handleDeleteMedia}
                thumbnailSize={150}
              />
              
              {error && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>Error: {error}</Text>
                </View>
              )}

              {isUploading && (
                <View style={styles.uploadSummary}>
                  <Text style={styles.uploadTitle}>
                    Uploading Media... ({Math.round(uploadProgress)}%)
                  </Text>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBar, 
                        { width: `${uploadProgress}%` }
                      ]} 
                    />
                  </View>
                  <View style={styles.uploadingIndicator}>
                    <ActivityIndicator size="small" color="#0066cc" />
                    <Text style={styles.uploadingText}>
                      Uploading {media.length} files...
                    </Text>
                  </View>
                </View>
              )}
            </>
          )}

          <View style={styles.submitbuttonContainer}>
            <TouchableOpacity
              style={styles.submitButton}
              onPress={saveToCase}
              disabled={isSaving || media.length === 0}
            >
              <Text style={styles.submitButtonText}>
                {isSaving ? 'Submitting...' : 'Submit'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {cameraVisible && (
          <View style={styles.cameraOverlay}>
            <PhotoCapture
              setUri={handlePhotoCaptured}
              backPressed={() => setCameraVisible(false)}
            />
          </View>
        )}
      {videoVisible && (
  <View style={styles.cameraOverlay}>
    <VideoRecordingComp
      setUri={handleVideoCaptured}
      backPressed={() => setVideoVisible(false)}
      onRecordingComplete={() => setVideoVisible(false)}
    />
  </View>
)}
        {isSuccessVisible && (
          <SuccessScreen
            message="All the selected evidences are dispatched successfully!"
            duration={3000}
          />
        )}
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
  },
  errorContainer: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#ffecec',
    borderRadius: 5,
    marginTop: 10,
  },
  errorText: {
    color: '#ff0000',
  },
  uploadSummary: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 5,
    marginTop: 10,
  },
  uploadTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  uploadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  uploadingText: {
    marginLeft: 10,
    color: '#0066cc',
  },
  submitButton: {
    marginTop: 10,
    backgroundColor: '#0B36A1',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  submitbuttonContainer: {
    paddingHorizontal: 20,
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginVertical: 5,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
});

export default CapturePackageBox; 