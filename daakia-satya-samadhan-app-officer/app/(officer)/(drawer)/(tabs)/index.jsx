import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, Modal, Alert } from "react-native";
import QRScanner from "../../../../components/Larges/QRScanner";
import { useRouter } from 'expo-router';
import { apiService } from "../../../../services/api";
import { useAuth } from "../../../../context/auth-context";
export default function HomeOfficer() {

  const router = useRouter();
  const { token } = useAuth();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [scannedData, setScannedData] = useState(null);

  const handleScanComplete = async (data) => {
    setScannedData(data);
    setIsModalVisible(false);

    try {
      const url = new URL(data);
      const path = url.pathname;

      if (path.includes('/cases/') && path.includes('/evidences/')) {
        const segments = path.split('/');
        const caseid = segments[2];
        const evidenceId = segments[4];

        router.push({
          pathname: '/(officer)/(screens)/evidenceDetails',
          params: { caseid, evidenceId }
        });
      }
      else if (path.includes('/cases/') && !path.includes('/evidences/')) {
        const segments = path.split('/');
        const caseid = segments[2];

        router.push({
          pathname: '/(officer)/(screens)/caseDetails',
          params: { caseid }
        });
      }
      else if (path.includes('/evidencePackaging/')) {
        const segments = path.split('/');
        const boxId = segments[2];
        
        try {
          const response = await apiService.getEvidencesOfABox(token, boxId);
          // console.log("Evidence Packaging Data:", JSON.stringify(response, null, 2));
          
          // Navigate to case details if API call is successful
          if (response.status === 'success' && response.data?.evidencePackaging?.evidenceId?.length > 0) {
            const caseId = response.data.evidencePackaging.evidenceId[0].caseId._id;
            router.push({
              pathname: '/(officer)/(screens)/caseDetails',
              params: { caseid: caseId }
            });
          }
        } catch (apiError) {
          console.error("API Error:", apiError);
        }
      }
      else if (path.includes('/forensicRequests/')) {
        Alert.alert(
          "Incorrect Scanner",
          "Please use the Dispatcher scanner for forensic requests",
          [{ text: "OK", onPress: () => console.log("OK Pressed") }],
          { cancelable: false }
        );
      }
      else {
        console.log("Invalid QR Code Data:", data);
        Alert.alert(
          "Invalid QR Code",
          "The scanned QR code is not in a recognized format",
          [{ text: "OK", onPress: () => console.log("OK Pressed") }],
          { cancelable: false }
        );
      }
    } catch (error) {
      console.log("Invalid QR Code Data:", data);
      console.error("Error parsing URL:", error);
      Alert.alert(
        "Invalid QR Code",
        "Could not process the scanned QR code",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
    }
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };


  return (
    <View style={styles.container}>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.caseButton} onPress={() => router.push('/(officer)/(screens)/recentCases')}>
          <Image source={require('../../../../assets/images/recent_cases.png')} style={styles.buttonIcon} />
          <Text style={styles.buttonText}>Recent Cases</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.caseButton} onPress={() => router.push('/(officer)/(screens)/newcase')}>
          <Image source={require('../../../../assets/images/new_case.png')} style={styles.buttonIcon} />
          <Text style={styles.buttonText}>New Case</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.qrSection} onPress={() => setIsModalVisible(true)}>
        <Text style={styles.qrTitle}>View a case</Text>
        <Text style={styles.qrSubtitle}>Scan QR to view any case</Text>
        <Image source={require('../../../../assets/images/image.png')} style={styles.qrImage} />
      </TouchableOpacity>

      <Modal visible={isModalVisible} animationType="slide">
        <QRScanner onScanComplete={handleScanComplete} onClose={handleCloseModal} />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    padding: 30,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 50,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",

  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0B36A1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: "#0B36A1",
    textAlign: "center",
    fontFamily: 'Roboto_bold',
  },
  qrSection: {
    alignItems: "center",
    marginVertical: 30,
  },
  qrTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#0B36A1",
    fontFamily: 'Roboto_bold',
  },
  qrSubtitle: {
    fontSize: 16,
    color: "gray",
    marginBottom: 15,
    fontFamily: 'Roboto',
  },
  qrImage: {
    width: 180,
    height: 180,
    resizeMode: "contain",
    marginLeft: 13,
  },
  logoutContainer: {
    position: "absolute",
    bottom: 50,
    alignItems: "center",
  },
  userText: {
    fontSize: 18,
    marginBottom: 12,
    fontWeight: "bold",
    fontFamily: 'Roboto',
  },
});