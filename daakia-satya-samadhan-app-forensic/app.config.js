import 'dotenv/config';
import { config } from 'dotenv';

const env = process.env.APP_ENV || 'development';
config({ path: `.env.${env}` });

const getExtraConfig = () => {
  return {
    baseUrl: process.env.API_BASE_URL,
    uploadUrl: process.env.API_UPLOAD_URL,
    router: {
      origin: false
    },
    appEnv: env,
    eas: {
      projectId: "04d0481a-a367-435a-a162-cc0ec81f4c91"
    }
  };
};

export default {
  expo: {
    scheme: "myapp",
    name: "<PERSON><PERSON><PERSON>ensic",
    slug: "<PERSON><PERSON><PERSON>-<PERSON><PERSON>han-Forensic",
    version: "0.22.19",
    orientation: "portrait",
    icon: "./assets/images/android-icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./assets/images/android-icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
      userInterfaceStyle: "light"
    },
    ios: {
      supportsTablet: true,
      infoPlist: {
        NSCameraUsageDescription: "Allow <PERSON><PERSON><PERSON>ensic to access your camera"
      },
      bundleIdentifier: "com.daakia.satyaSamadhanForensic.app",
      config: {
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
      }
    },
    android: {
      package: "com.daakia.satyaSamadhanForensic.app",
      adaptiveIcon: {
        foregroundImage: "./assets/images/android-icon.png",
        backgroundColor: "#ffffff"
      },
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.MODIFY_AUDIO_SETTINGS"
      ],
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_API_KEY
        }
      }
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/android-icon.png"
    },
    extra: getExtraConfig(),
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/android-icon.png",
          resizeMode: "cover",
          imageWidth: 200
        }
      ],
      [
        "expo-media-library",
        {
          photosPermission: "Allow Satya Samadhan Forensic to access your photos.",
          savePhotosPermission: "Allow Satya Samadhan Forensic to save photos.",
          isAccessMediaLocationEnabled: false
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "Allow Satya Samadhan Forensic to access your photos to enable image selection.",
          cameraPermission: "Allow Satya Samadhan Forensic to use your camera for capturing photos."
        }
      ],
      [
        "expo-camera",
        {
          cameraPermission: "Allow Satya Samadhan Forensic to access your camera for photo and video capture.",
          microphonePermission: "Allow Satya Samadhan Forensic to access your microphone for recording audio during video capture."
        }
      ],
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "Allow Satya Samadhan Forensic to access your location to provide location-based services."
        }
      ],
      [
        "expo-av",
        {
          microphonePermission: "Allow Satya Samadhan Forensic to access your microphone."
        }
      ],
      "expo-secure-store",
      "expo-video",
      "react-native-vision-camera",
      [
        "react-native-document-scanner-plugin",
        {
          cameraPermission: "Allow Satya Samadhan Forensic to access your camera for document scanning."
        }
      ]
    ],
    runtimeVersion: {
      policy: "appVersion"
    },
    experiments: {
      typedRoutes: true
    },
  }
}; 