export const AUTH_ERRORS = {
  INVALID_MOBILE: 'Mobile number must be exactly 10 digits.',
  INVALID_OTP: 'Please enter a valid 5-digit OTP',
  TOKEN_EXPIRED: 'Token expired or invalid',
  PROFILE_FETCH_FAILED: 'Failed to fetch user profile',
  BASE_URL_MISSING: 'Base URL is not configured.',
  TOKEN_REFRESH_FAILED: 'Token refresh failed',
  MISSING_AUTH: 'Missing authentication data',
  INVALID_NAME: 'Name must be at least 2 characters long',
  INVALID_AADHAR: 'Please enter a valid 12-digit Aadhar number.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  MISSING_LAB: 'Please select a lab.',
  MISSING_DEPARTMENT: 'Please select a department.',
  MISSING_PROFILE_PIC: 'Please upload your profile picture.',
  REGISTRATION_FAILED: 'Registration failed.',
  FETCH_LABS_FAILED: 'Failed to fetch labs.',
  FETCH_DEPARTMENTS_FAILED: 'Failed to fetch departments.',
  UPLOAD_FAILED: 'Failed to upload file.',
  UPLOAD_ERROR: 'An error occurred while uploading the file.',
  CAMERA_PERMISSION_DENIED: 'Sorry, we need camera permissions to take photos!',
  GALLERY_PERMISSION_DENIED: 'Sorry, we need camera roll permissions to make this work!',
  REPORT_SUBMISSION_FAILED: 'Failed to submit report',
  MISSING_TITLE: 'Please enter a title for the report',
  MISSING_DESCRIPTION: 'Please enter a description for the report',
  MISSING_MEDIA: 'Please add at least one photo or PDF document',
  INVALID_ROLE: 'Invalid role selected',
};

export const STORAGE_KEYS = {
  TOKEN: 'token',
  ROLES: 'roles',
  SELECTED_ROLE: 'selectedRole',
  CATEGORY: 'category',
  USER_ID: 'userId',
  REQUEST_ID: 'requestId',
};

export const TOKEN_REFRESH_INTERVAL = 2.5 * 60 * 60 * 1000; // 2.5 hours in milliseconds

// Base endpoints
const API_BASE = '/api';
const USER_BASE = `${API_BASE}/user`;
const FORENSIC_BASE = `${API_BASE}/forensic`;
const CASE_BASE = `${API_BASE}/case`;

export const API_ENDPOINTS = {
  // User related endpoints
  LOGIN: `${USER_BASE}/login`,
  VERIFY_OTP: `${USER_BASE}/login/verify`,
  REFRESH_TOKEN: `${USER_BASE}/refreshToken`,
  PROFILE: `${USER_BASE}/profile`,
  REGISTER: `${USER_BASE}/forensic/register`,

  // Forensic related endpoints
  FETCH_LABS: `${FORENSIC_BASE}/lab`,
  FETCH_DEPARTMENTS: `${FORENSIC_BASE}/lab/:labId/department`,
  FETCH_RECENT_CASES: `${FORENSIC_BASE}/lab/case/status`,
  REPORT_ACCESS: `${FORENSIC_BASE}/report/access/:reportId`,
  UPDATE_REPORT_ACCESS: `${FORENSIC_BASE}/report/access`,
  FETCH_CASE_REPORTS: `${FORENSIC_BASE}/report/case/:caseId`,
  FETCH_FORENSIC_REQUEST_EVIDENCES: `${FORENSIC_BASE}/request/:requestId/evidences`,
  FETCH_EVIDENCE_DETAILS: `${API_BASE}/case/:caseId/evidences/:evidenceId`,
  SEND_EVIDENCE_TO_NEXT_LAB: `${FORENSIC_BASE}/request/send/:evidenceId`,
  UPDATE_FORENSIC_REQUEST: `${FORENSIC_BASE}/request/:requestId`,

  // File upload endpoint
  UPLOAD: `${API_BASE}/upload`,

  // Report submission endpoint
  SUBMIT_REPORT: `${FORENSIC_BASE}/report/case/:caseId/evidence/:evidenceId`,

  // Evidence box packaging endpoint
  EVIDENCE_BOX_PACKAGING_BY_ID: `${CASE_BASE}/evidence/box/packaging/:boxId`,
};