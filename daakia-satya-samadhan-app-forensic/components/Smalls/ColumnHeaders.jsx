import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

const createStyles = (width, height) => StyleSheet.create({
  columnHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: width * 0.05,
    marginBottom: height * 0.01,
  },
  columnHeaderText: {
    opacity: 0.5,
    color: Colors.lightText,
  },
  columnTimeContainer: {
    flexDirection: "row",
    width: width * 0.4,
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeHeaderText: {
    width: width * 0.2,
    textAlign: "center",
    opacity: 0.5,
    color: Colors.lightText,
  },
  dateHeaderText: {
    textAlign: "right",
    opacity: 0.5,
    color: Colors.lightText,
  },
});

const ColumnHeaders = ({ width, height }) => {
  const styles = createStyles(width, height);

  return (
    <View style={styles.columnHeaderContainer}>
      <Text style={styles.columnHeaderText}>Case Name</Text>
      <View style={styles.columnTimeContainer}>
        <Text style={styles.timeHeaderText}>Time</Text>
        <Text style={styles.dateHeaderText}>Date</Text>
      </View>
    </View>
  );
};

export default ColumnHeaders; 