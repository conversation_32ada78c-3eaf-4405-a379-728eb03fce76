import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
  TextInput
} from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { Colors } from '../../constants/colors';
import { apiService } from '../../services/api';
import { useAuth } from '../../context/auth-context';
import { transformUrl } from '../../utils/transformUrl';

const EvidencePackagingModal = ({
  visible,
  onClose,
  evidencePackagingId,
  fullUrl
}) => {
  const [loading, setLoading] = useState(false);
  const [evidenceData, setEvidenceData] = useState(null);
  const [caseTitle, setCaseTitle] = useState('');
  const [processingEvidence, setProcessingEvidence] = useState(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [currentRejectData, setCurrentRejectData] = useState(null);
  const { token } = useAuth();

  useEffect(() => {
    if (visible && evidencePackagingId && token) {
      fetchEvidenceBoxData();
    }
  }, [visible, evidencePackagingId, token]);

  const fetchEvidenceBoxData = async () => {
    setLoading(true);
    try {
      console.log("Fetching evidence box data for ID:", evidencePackagingId);
      console.log("Full URL:", fullUrl);

      // Use evidencePackagingId as boxId for the API call
      const result = await apiService.getEvidencesOfABox(token, evidencePackagingId);

      console.log("Evidence box API response:", JSON.stringify(result, null, 2));

      if (result.status === 'success') {
        setEvidenceData(result.data);

        // Get case title from the first evidence's caseId object
        const firstEvidence = result.data?.evidencePackaging?.evidenceId?.[0];
        if (firstEvidence?.caseId?.title) {
          setCaseTitle(firstEvidence.caseId.title);
        }
      } else {
        console.error("API Error:", result.message);
        Alert.alert("Error", result.message || "Failed to fetch evidence box data");
      }
    } catch (error) {
      console.error("Network Error:", error);
      Alert.alert("Error", "Network error: " + error.message);
    } finally {
      setLoading(false);
    }
  };



  const handleClose = () => {
    setEvidenceData(null);
    setCaseTitle('');
    setProcessingEvidence(null);
    onClose();
  };

  // Accept evidence function
  const handleAcceptEvidence = async (forensicRequestId, evidenceId) => {
    try {
      setProcessingEvidence(evidenceId);

      const result = await apiService.updateForensicRequest(token, forensicRequestId, 'received', '');

      if (result.status === 'success') {
        // Update local state
        setEvidenceData(prev => ({
          ...prev,
          evidencePackaging: {
            ...prev.evidencePackaging,
            evidenceId: prev.evidencePackaging.evidenceId.map(evidence =>
              evidence._id === evidenceId
                ? {
                    ...evidence,
                    forensicRequests: evidence.forensicRequests.map(req =>
                      req._id === forensicRequestId
                        ? { ...req, status: 'received' }
                        : req
                    )
                  }
                : evidence
            )
          }
        }));

        Alert.alert('Success', 'Evidence accepted successfully!');
      } else {
        Alert.alert('Error', result.message || 'Failed to accept evidence');
      }
    } catch (error) {
      Alert.alert('Error', 'Network error: ' + error.message);
    } finally {
      setProcessingEvidence(null);
    }
  };

  // Reject evidence function
  const handleRejectEvidence = (forensicRequestId, evidenceId) => {
    setCurrentRejectData({ forensicRequestId, evidenceId });
    setShowRejectModal(true);
  };

  // Confirm rejection with reason
  const confirmRejectEvidence = async () => {
    if (!rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for rejection');
      return;
    }

    try {
      setProcessingEvidence(currentRejectData.evidenceId);
      setShowRejectModal(false);

      const result = await apiService.updateForensicRequest(
        token,
        currentRejectData.forensicRequestId,
        'rejected',
        rejectionReason.trim()
      );

      if (result.status === 'success') {
        // Update local state
        setEvidenceData(prev => ({
          ...prev,
          evidencePackaging: {
            ...prev.evidencePackaging,
            evidenceId: prev.evidencePackaging.evidenceId.map(evidence =>
              evidence._id === currentRejectData.evidenceId
                ? {
                    ...evidence,
                    forensicRequests: evidence.forensicRequests.map(req =>
                      req._id === currentRejectData.forensicRequestId
                        ? { ...req, status: 'rejected' }
                        : req
                    )
                  }
                : evidence
            )
          }
        }));

        Alert.alert('Success', 'Evidence rejected successfully!');
      } else {
        Alert.alert('Error', result.message || 'Failed to reject evidence');
      }
    } catch (error) {
      Alert.alert('Error', 'Network error: ' + error.message);
    } finally {
      setProcessingEvidence(null);
      setRejectionReason('');
      setCurrentRejectData(null);
    }
  };

  // Cancel rejection
  const cancelRejectEvidence = () => {
    setShowRejectModal(false);
    setRejectionReason('');
    setCurrentRejectData(null);
  };



  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Evidence Package</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading evidence details...</Text>
            </View>
          ) : (
            <View style={styles.detailsContainer}>
              {/* Case Title Label */}
              {caseTitle && (
                <View style={styles.infoRow}>
                  <Text style={styles.label}>Case Title:</Text>
                  <Text style={styles.value}>{caseTitle}</Text>
                </View>
              )}

              {/* Case Description Label */}
              {evidenceData?.evidencePackaging?.evidenceId?.[0]?.caseId?.description && (
                <View style={styles.infoRow}>
                  <Text style={styles.label}>Case Description:</Text>
                  <Text style={styles.value}>{evidenceData.evidencePackaging.evidenceId[0].caseId.description}</Text>
                </View>
              )}

              {/* Total Evidence Count Label */}
              {evidenceData?.evidencePackaging?.evidenceId && (
                <View style={styles.infoRow}>
                  <Text style={styles.label}>Total Evidence:</Text>
                  <Text style={styles.value}>{evidenceData.evidencePackaging.evidenceId.length}</Text>
                </View>
              )}

              {/* Evidence Cards */}
              {evidenceData?.evidencePackaging?.evidenceId?.map((evidence) => (
                <View key={evidence._id} style={styles.evidenceCard}>
                  {/* Evidence Details */}
                  <View style={styles.evidenceDetails}>
                    {/* Lab Assignment (First) */}
                    {evidence.labAssignments && evidence.labAssignments.length > 0 && (
                      <View style={styles.detailRow}>
                        <Text style={styles.evidenceLabel}>Lab:</Text>
                        <Text style={styles.evidenceType}>{evidence.labAssignments[0].labId?.name}</Text>
                      </View>
                    )}

                    {/* Department and Priority (Second) */}
                    {evidence.labAssignments && evidence.labAssignments.length > 0 && (
                      <View style={styles.detailRow}>
                        <Text style={styles.evidenceLabel}>Dept:</Text>
                        <Text style={styles.evidenceType}>
                          {evidence.labAssignments[0].labDepartmentId?.name} (Priority: {evidence.labAssignments[0].priority})
                        </Text>
                      </View>
                    )}

                    {/* Evidence Title (Third) */}
                    <View style={styles.detailRow}>
                      <Text style={styles.evidenceLabel}>Evidence Title:</Text>
                      <Text style={styles.evidenceType}>{evidence.title}</Text>
                    </View>

                    {/* Evidence Package Photos (Fourth) */}
                    {evidence.packagingUrl && evidence.packagingUrl.length > 0 && (
                      <View style={styles.photoSection}>
                        <Text style={styles.photoSectionTitle}>Evidence Package Photo:</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.photoScroll}>
                          {evidence.packagingUrl.map((url, photoIndex) => (
                            <Image key={photoIndex} source={{ uri: transformUrl(url) }} style={styles.photo} />
                          ))}
                        </ScrollView>
                      </View>
                    )}

                    {/* Evidence Photos (Fifth) */}
                    {evidence.forensicRequests?.[0]?.evidence?.attachmentUrl && (
                      <View style={styles.photoSection}>
                        <Text style={styles.photoSectionTitle}>Evidence Photo:</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.photoScroll}>
                          {evidence.forensicRequests[0].evidence.attachmentUrl.map((url, photoIndex) => (
                            <Image key={photoIndex} source={{ uri: transformUrl(url) }} style={styles.photo} />
                          ))}
                        </ScrollView>
                      </View>
                    )}

                    {/* Additional Lab Assignments */}
                    {evidence.labAssignments && evidence.labAssignments.length > 1 && (
                      <View style={styles.additionalLabSection}>
                        <Text style={styles.additionalLabTitle}>
                          After completion here, send to these  evidence labs also for listed lab / depatment labs:
                        </Text>
                        {evidence.labAssignments.slice(1).map((assignment) => (
                          <View key={assignment._id} style={styles.additionalLabItem}>
                            <Text style={styles.additionalLabText}>
                              • {assignment.labId?.name} - {assignment.labDepartmentId?.name} (Priority: {assignment.priority})
                            </Text>
                          </View>
                        ))}
                      </View>
                    )}

                    {/* Accept/Reject Buttons */}
                    {evidence.forensicRequests && evidence.forensicRequests.length > 0 && (
                      <View style={styles.actionButtonsContainer}>
                        {evidence.forensicRequests[0].status === 'dispatched' ? (
                          <View style={styles.actionButtons}>
                            <TouchableOpacity
                              style={[styles.actionButton, styles.rejectButton]}
                              onPress={() => handleRejectEvidence(evidence.forensicRequests[0]._id, evidence._id)}
                              disabled={processingEvidence === evidence._id}
                            >
                              {processingEvidence === evidence._id ? (
                                <ActivityIndicator size="small" color="#fff" />
                              ) : (
                                <>
                                  <MaterialCommunityIcons name="close-circle" size={16} color="#fff" />
                                  <Text style={styles.actionButtonText}>Reject</Text>
                                </>
                              )}
                            </TouchableOpacity>

                            <TouchableOpacity
                              style={[styles.actionButton, styles.acceptButton]}
                              onPress={() => handleAcceptEvidence(evidence.forensicRequests[0]._id, evidence._id)}
                              disabled={processingEvidence === evidence._id}
                            >
                              {processingEvidence === evidence._id ? (
                                <ActivityIndicator size="small" color="#fff" />
                              ) : (
                                <>
                                  <MaterialCommunityIcons name="check-circle" size={16} color="#fff" />
                                  <Text style={styles.actionButtonText}>Accept</Text>
                                </>
                              )}
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <View style={styles.statusContainer}>
                            <MaterialCommunityIcons
                              name={evidence.forensicRequests[0].status === 'received' ? 'check-circle' : 'close-circle'}
                              size={20}
                              color={evidence.forensicRequests[0].status === 'received' ? '#4CAF50' : '#F44336'}
                            />
                            <Text style={[
                              styles.statusText,
                              { color: evidence.forensicRequests[0].status === 'received' ? '#4CAF50' : '#F44336' }
                            ]}>
                              {evidence.forensicRequests[0].status === 'received' ? 'Accepted' : 'Rejected'}
                            </Text>
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                </View>
              ))}

           
            </View>
          )}
        </ScrollView>


      </View>

      {/* Rejection Reason Modal */}
      <Modal
        visible={showRejectModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelRejectEvidence}
      >
        <View style={styles.rejectModalOverlay}>
          <View style={styles.rejectModalContainer}>
            <View style={styles.rejectModalHeader}>
              <Text style={styles.rejectModalTitle}>Reject Evidence</Text>
              <TouchableOpacity onPress={cancelRejectEvidence}>
                <MaterialCommunityIcons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <Text style={styles.rejectModalText}>
              Please provide a reason for rejecting this evidence:
            </Text>

            <TextInput
              style={styles.rejectReasonInput}
              value={rejectionReason}
              onChangeText={setRejectionReason}
              placeholder="Enter rejection reason..."
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
            />

            <View style={styles.rejectModalButtons}>
              <TouchableOpacity
                style={[styles.rejectModalButton, styles.cancelButton]}
                onPress={cancelRejectEvidence}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.rejectModalButton, styles.confirmRejectButton]}
                onPress={confirmRejectEvidence}
              >
                <Text style={styles.confirmRejectButtonText}>Reject</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  detailsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 15,
  },
  infoRow: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  value: {
    fontSize: 16,
    color: '#333',
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },

  evidenceCard: {
    borderColor: Colors.border || '#e0e0e0',
    borderWidth: 1,
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  evidenceDetails: {
    flex: 1,
    gap: 6,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black || '#000',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText || '#666',
    width: 80,
    fontWeight: '500',
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText || '#666',
    flex: 1,
  },
  viewButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoSection: {
    marginVertical: 8,
  },
  photoSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  photoScroll: {
    flexDirection: 'row',
  },
  photo: {
    width: 60,
    height: 60,
    borderRadius: 6,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  additionalLabSection: {
    marginTop: 12,
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
  },
  additionalLabTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 8,
  },
  additionalLabItem: {
    marginBottom: 4,
  },
  additionalLabText: {
    fontSize: 12,
    color: '#555',
    lineHeight: 16,
  },
  actionButtonsContainer: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    gap: 6,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    gap: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  // Rejection Modal Styles
  rejectModalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  rejectModalContainer: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  rejectModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  rejectModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  rejectModalText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  rejectReasonInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#f8f9fa',
    minHeight: 100,
    marginBottom: 20,
  },
  rejectModalButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  rejectModalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmRejectButton: {
    backgroundColor: '#F44336',
  },
  confirmRejectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },

});

export default EvidencePackagingModal;
