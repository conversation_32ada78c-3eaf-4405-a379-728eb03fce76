import { AUTH_ERRORS } from '../constants/auth';

export const validationService = {
  validateMobile: (mobileNumber) => {
    if (!mobileNumber || mobileNumber.length !== 10) {
      throw new Error(AUTH_ERRORS.INVALID_MOBILE);
    }
  },

  validateOtp: (otp) => {
    if (!otp || otp.length !== 5) {
      throw new Error(AUTH_ERRORS.INVALID_OTP);
    }
  },

  validateLoginData: (mobileNumber) => {
    validationService.validateMobile(mobileNumber);
  },

  validateOtpVerification: (userId, requestId, otp) => {
    if (!userId || !requestId) {
      throw new Error(AUTH_ERRORS.MISSING_AUTH);
    }
    validationService.validateOtp(otp);
  },

  validateName: (name) => {
    if (!name || name.trim().length < 2) {
      throw new Error(AUTH_ERRORS.INVALID_NAME);
    }
  },

  validateAadhar: (aadhar) => {
    if (!aadhar || aadhar.length !== 12 || isNaN(aadhar)) {
      throw new Error(AUTH_ERRORS.INVALID_AADHAR);
    }
  },

  validateEmail: (email) => {
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }
  },

  validateRegistrationData: (data) => {
    const { name, mobileNumber, aadhar, emailId, lab, labDepartment, displayUrl } = data;

    validationService.validateName(name);
    validationService.validateMobile(mobileNumber);
    validationService.validateAadhar(aadhar);
    validationService.validateEmail(emailId);

    if (!lab) throw new Error(AUTH_ERRORS.MISSING_LAB);
    if (!labDepartment) throw new Error(AUTH_ERRORS.MISSING_DEPARTMENT);
    if (!displayUrl || typeof displayUrl !== 'string' || !displayUrl.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_PROFILE_PIC);
    }
  },

  validateReportData: (title, description, media) => {
    if (!title || !title.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_TITLE);
    }

    if (!description || !description.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_DESCRIPTION);
    }

    if (!media || media.length === 0) {
      throw new Error(AUTH_ERRORS.MISSING_MEDIA);
    }
  },

  validateRecentCasesStatus: (status) => {
    if (!status || !Object.values(RECENT_CASES_STATUS).includes(status)) {
      throw new Error('Invalid case status');
    }
  },

  // New validations for case details
  validateForensicRequestId: (requestId) => {
    if (!requestId || typeof requestId !== 'string' || !requestId.trim()) {
      throw new Error('Invalid forensic request ID');
    }
  },

  validateCaseId: (caseId) => {
    if (!caseId || typeof caseId !== 'string' || !caseId.trim()) {
      throw new Error('Invalid case ID');
    }
  },

  validateReportId: (reportId) => {
    if (!reportId || typeof reportId !== 'string' || !reportId.trim()) {
      throw new Error('Invalid report ID');
    }
  },

  validateAccessDuration: (duration) => {
    const numDuration = parseInt(duration);
    if (isNaN(numDuration) || numDuration <= 0) {
      throw new Error('Invalid access duration');
    }
  },

  validateAccessRequest: (requestId, access, duration) => {
    if (!requestId || typeof requestId !== 'string' || !requestId.trim()) {
      throw new Error('Invalid access request ID');
    }
    if (!access || !['granted', 'rejected'].includes(access)) {
      throw new Error('Invalid access status');
    }
    if (access === 'granted') {
      validationService.validateAccessDuration(duration);
    }
  },
}; 


export const LAB_STATUS = {
  INITIATED: "initiated",
  DISPATCHED: "dispatched",
  RECEIVED: "received",
  REJECTED: "rejected",
  COMPLETED: "completed",
};

export const LAB_STATUS_DISPLAY_NAMES = {
  [LAB_STATUS.INITIATED]: "Dispatched to Malkhana",
  [LAB_STATUS.DISPATCHED]: "Arrived at Lab",
  [LAB_STATUS.RECEIVED]: "Accepted in Lab",
  [LAB_STATUS.REJECTED]: "Rejected by Lab",
  [LAB_STATUS.COMPLETED]: "Report Generated",
};

// Filtered status for recent cases - only Accepted in Lab and Report Generated
export const RECENT_CASES_STATUS = {
  RECEIVED: LAB_STATUS.RECEIVED,
  COMPLETED: LAB_STATUS.COMPLETED,
};

export const RECENT_CASES_STATUS_DISPLAY_NAMES = {
  [RECENT_CASES_STATUS.RECEIVED]: LAB_STATUS_DISPLAY_NAMES[LAB_STATUS.RECEIVED],
  [RECENT_CASES_STATUS.COMPLETED]: LAB_STATUS_DISPLAY_NAMES[LAB_STATUS.COMPLETED],
};