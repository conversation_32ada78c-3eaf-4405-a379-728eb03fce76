/**
 * Helper function to handle different types of scanned QR codes
 * @param {string} scannedData - The raw scanned data from QR code
 * @returns {object} - Object containing action type and extracted data
 */
export const handleScannedData = (scannedData) => {
  try {
    // Parse the URL
    const url = new URL(scannedData);
    const path = url.pathname;
    
    console.log("Parsed URL path:", path);
    
    // Check for evidence packaging URLs
    if (path.includes('/evidencePackaging/')) {
      const evidencePackagingId = path.split('/evidencePackaging/')[1];
      
      return {
        action: 'OPEN_EVIDENCE_PACKAGING_MODAL',
        data: {
          evidencePackagingId,
          fullUrl: scannedData
        }
      };
    }
    
    // Check for forensic request URLs
    if (path.includes('/forensicRequests/')) {
      const forensicRequestId = path.split('/forensicRequests/')[1];
      
      return {
        action: 'NAVIGATE_TO_CASE_DETAILS',
        data: {
          forensicRequestId,
          fullUrl: scannedData
        }
      };
    }
    
    // Unknown URL pattern
    return {
      action: 'SHOW_INVALID_QR_ALERT',
      data: {
        message: 'Unknown QR code pattern',
        scannedData
      }
    };
    
  } catch (error) {
    // Handle parsing errors
    console.error("Error parsing scanned data:", error);
    
    return {
      action: 'SHOW_PARSING_ERROR_ALERT',
      data: {
        message: 'Could not process the scanned QR code',
        scannedData,
        error: error.message
      }
    };
  }
};
